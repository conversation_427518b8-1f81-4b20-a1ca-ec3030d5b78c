---
// Giscus 评论组件
// 基于 GitHub Discussions 的评论系统

interface Props {
	repo: string;
	repoId: string;
	category: string;
	categoryId: string;
	mapping?: string;
	strict?: string;
	reactionsEnabled?: string;
	emitMetadata?: string;
	inputPosition?: string;
	theme?: string;
	lang?: string;
	loading?: string;
}

const {
	repo,
	repoId,
	category,
	categoryId,
	mapping = "pathname",
	strict = "0",
	reactionsEnabled = "1",
	emitMetadata = "0",
	inputPosition = "bottom",
	theme = "preferred_color_scheme",
	lang = "zh-CN",
	loading = "lazy",
} = Astro.props;

// 导入 Giscus 样式
import "@/styles/giscus.css";
---

<div class="giscus-wrapper mt-16">
	<hr class="border-solid mb-8" />
	<h2 class="title mb-6 text-xl before:hidden">评论</h2>

	<div
		class="giscus-container rounded-lg border border-gray-200 bg-white p-6 transition-all duration-300 hover:shadow-lg dark:border-gray-700 dark:bg-gray-900"
	>
		<script
			src="https://giscus.app/client.js"
			data-repo={repo}
			data-repo-id={repoId}
			data-category={category}
			data-category-id={categoryId}
			data-mapping={mapping}
			data-strict={strict}
			data-reactions-enabled={reactionsEnabled}
			data-emit-metadata={emitMetadata}
			data-input-position={inputPosition}
			data-theme={theme}
			data-lang={lang}
			data-loading={loading}
			crossorigin="anonymous"
			async></script>
	</div>
</div>

<script is:inline>
	// 简化的主题同步功能
	function updateGiscusTheme() {
		const iframe = document.querySelector("iframe.giscus-frame");
		if (!iframe) return;

		const theme = document.documentElement.getAttribute("data-theme");
		const giscusTheme = theme === "dark" ? "dark" : "light";

		if (iframe.contentWindow) {
			iframe.contentWindow.postMessage(
				{ giscus: { setConfig: { theme: giscusTheme } } },
				"https://giscus.app",
			);
		}
	}

	// 监听主题变化
	document.addEventListener("theme-change", function () {
		setTimeout(updateGiscusTheme, 100);
	});

	// 页面加载后初始化
	document.addEventListener("DOMContentLoaded", function () {
		// 立即设置正确的主题
		const currentTheme =
			document.documentElement.getAttribute("data-theme");
		console.log("Initial Giscus theme:", currentTheme);

		// 多次尝试确保主题正确设置
		setTimeout(updateGiscusTheme, 500);
		setTimeout(updateGiscusTheme, 1000);
		setTimeout(updateGiscusTheme, 2000);
	});

	// 监听 Giscus 加载完成
	window.addEventListener("message", function (event) {
		if (event.origin !== "https://giscus.app") return;
		if (event.data.giscus && event.data.giscus.discussion) {
			setTimeout(updateGiscusTheme, 100);
		}
	});
</script>
