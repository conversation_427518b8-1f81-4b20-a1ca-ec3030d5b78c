---
// Giscus 评论组件
// 基于 GitHub Discussions 的评论系统

interface Props {
	repo: string;
	repoId: string;
	category: string;
	categoryId: string;
	mapping?: string;
	strict?: string;
	reactionsEnabled?: string;
	emitMetadata?: string;
	inputPosition?: string;
	theme?: string;
	lang?: string;
	loading?: string;
}

const {
	repo,
	repoId,
	category,
	categoryId,
	mapping = "pathname",
	strict = "0",
	reactionsEnabled = "1",
	emitMetadata = "0",
	inputPosition = "bottom",
	theme = "preferred_color_scheme",
	lang = "zh-CN",
	loading = "lazy",
} = Astro.props;
---

<div class="giscus-container mt-16">
	<hr class="border-solid mb-8" />
	<h2 class="title mb-6 text-xl before:hidden">评论</h2>

	<script
		src="https://giscus.app/client.js"
		data-repo={repo}
		data-repo-id={repoId}
		data-category={category}
		data-category-id={categoryId}
		data-mapping={mapping}
		data-strict={strict}
		data-reactions-enabled={reactionsEnabled}
		data-emit-metadata={emitMetadata}
		data-input-position={inputPosition}
		data-theme={theme}
		data-lang={lang}
		data-loading={loading}
		crossorigin="anonymous"
		async></script>
</div>

<script>
	// 获取网站主题色配置
	function getWebsiteThemeColors() {
		const root = document.documentElement;
		const isDark = root.getAttribute("data-theme") === "dark";

		// 获取 CSS 变量值
		const computedStyle = getComputedStyle(root);

		return {
			// 主要颜色
			bg: computedStyle.getPropertyValue("--color-global-bg").trim(),
			text: computedStyle.getPropertyValue("--color-global-text").trim(),
			accent: computedStyle.getPropertyValue("--color-accent").trim(),
			accent2: computedStyle.getPropertyValue("--color-accent-2").trim(),
			link: computedStyle.getPropertyValue("--color-link").trim(),
			quote: computedStyle.getPropertyValue("--color-quote").trim(),
			isDark,
		};
	}

	// 创建自定义 Giscus 主题样式注入
	function injectCustomStyles() {
		// 等待 iframe 加载完成后注入样式
		const iframe = document.querySelector<HTMLIFrameElement>(
			"iframe.giscus-frame",
		);
		if (!iframe) return;

		// 监听 iframe 加载完成
		iframe.addEventListener("load", () => {
			try {
				const iframeDoc =
					iframe.contentDocument || iframe.contentWindow?.document;
				if (!iframeDoc) return;

				// 创建样式元素
				const style = iframeDoc.createElement("style");
				style.id = "custom-giscus-theme";

				const colors = getWebsiteThemeColors();

				// 自定义 CSS 样式，使用网站的颜色变量
				style.textContent = `
					/* 主容器样式 */
					.gsc-main {
						font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace !important;
					}

					/* 评论框样式 */
					.gsc-comment-box {
						border: 1px solid oklch(from ${colors.text} l c h / 0.1) !important;
						border-radius: 8px !important;
						background: ${colors.bg} !important;
					}

					.gsc-comment-box-textarea {
						background: ${colors.bg} !important;
						color: ${colors.text} !important;
						border: 1px solid oklch(from ${colors.text} l c h / 0.1) !important;
						font-family: inherit !important;
					}

					.gsc-comment-box-textarea:focus {
						border-color: ${colors.accent} !important;
						box-shadow: 0 0 0 2px oklch(from ${colors.accent} l c h / 0.2) !important;
					}

					/* 按钮样式 */
					.gsc-comment-box-button {
						background: ${colors.accent} !important;
						color: white !important;
						border: none !important;
						border-radius: 6px !important;
						font-weight: 500 !important;
					}

					.gsc-comment-box-button:hover:not(:disabled) {
						background: oklch(from ${colors.accent} calc(l * 0.9) c h) !important;
					}

					/* 评论样式 */
					.gsc-comment {
						border: 1px solid oklch(from ${colors.text} l c h / 0.08) !important;
						border-radius: 8px !important;
						background: oklch(from ${colors.bg} l c h / 0.5) !important;
					}

					.gsc-comment-author {
						color: ${colors.accent2} !important;
						font-weight: 600 !important;
					}

					.gsc-comment-content {
						color: ${colors.text} !important;
					}

					.gsc-comment-content a {
						color: ${colors.link} !important;
						text-decoration: underline !important;
						text-underline-offset: 3px !important;
					}

					.gsc-comment-content a:hover {
						text-decoration-thickness: 2px !important;
					}

					.gsc-comment-content blockquote {
						color: ${colors.quote} !important;
						border-left: 3px solid ${colors.accent} !important;
						background: oklch(from ${colors.bg} l c h / 0.3) !important;
					}

					/* 反应按钮样式 */
					.gsc-reactions-button {
						background: ${colors.bg} !important;
						border: 1px solid oklch(from ${colors.text} l c h / 0.1) !important;
						color: ${colors.text} !important;
						border-radius: 6px !important;
					}

					.gsc-reactions-button:hover {
						background: oklch(from ${colors.accent} l c h / 0.1) !important;
						border-color: ${colors.accent} !important;
					}

					/* 回复按钮 */
					.gsc-reply-button {
						color: ${colors.accent} !important;
					}

					.gsc-reply-button:hover {
						color: oklch(from ${colors.accent} calc(l * 0.8) c h) !important;
					}

					/* 时间线样式 */
					.gsc-timeline {
						border-color: oklch(from ${colors.text} l c h / 0.1) !important;
					}

					/* 加载状态 */
					.gsc-loading {
						color: ${colors.text} !important;
					}

					/* 头像样式 */
					.gsc-comment-author-avatar {
						border-radius: 50% !important;
					}

					/* 链接样式统一 */
					a {
						color: ${colors.link} !important;
					}

					a:hover {
						color: oklch(from ${colors.link} calc(l * 0.8) c h) !important;
					}
				`;

				// 移除旧样式（如果存在）
				const existingStyle = iframeDoc.getElementById(
					"custom-giscus-theme",
				);
				if (existingStyle) {
					existingStyle.remove();
				}

				// 添加新样式
				iframeDoc.head.appendChild(style);
			} catch (error) {
				console.warn("无法注入自定义 Giscus 样式:", error);
			}
		});
	}

	// 主题同步功能
	function updateGiscusTheme() {
		const iframe = document.querySelector<HTMLIFrameElement>(
			"iframe.giscus-frame",
		);
		if (!iframe) return;

		const theme = document.documentElement.getAttribute("data-theme");
		const giscusTheme = theme === "dark" ? "dark" : "light";

		// 发送主题更新消息
		iframe.contentWindow?.postMessage(
			{ giscus: { setConfig: { theme: giscusTheme } } },
			"https://giscus.app",
		);

		// 注入自定义样式
		setTimeout(() => {
			injectCustomStyles();
		}, 200);
	}

	// 添加加载状态管理
	function setLoadingState(loading: boolean) {
		const container = document.querySelector(".giscus-container");
		if (container) {
			if (loading) {
				container.classList.add("loading");
			} else {
				container.classList.remove("loading");
			}
		}
	}

	// 监听博客主题变化事件（与 ThemeToggle.astro 同步）
	document.addEventListener("theme-change", () => {
		setTimeout(updateGiscusTheme, 100);
	});

	// 监听 Astro 页面切换事件
	document.addEventListener("astro:after-swap", () => {
		setTimeout(updateGiscusTheme, 500);
	});

	// 页面加载完成后初始化
	document.addEventListener("DOMContentLoaded", () => {
		setLoadingState(true);

		// 等待 Giscus iframe 加载
		setTimeout(() => {
			updateGiscusTheme();
			setLoadingState(false);
		}, 1000);
	});

	// 监听 Giscus 相关事件
	window.addEventListener("message", (event) => {
		if (event.origin !== "https://giscus.app") return;

		if (event.data.giscus?.discussion) {
			// Giscus 已加载完成
			setLoadingState(false);
			updateGiscusTheme();
		}

		if (event.data.giscus?.error) {
			// 处理加载错误
			setLoadingState(false);
			console.warn("Giscus loading error:", event.data.giscus.error);
		}
	});

	// 备用方案：监听 data-theme 属性变化
	const observer = new MutationObserver((mutations) => {
		mutations.forEach((mutation) => {
			if (
				mutation.type === "attributes" &&
				mutation.attributeName === "data-theme"
			) {
				setTimeout(updateGiscusTheme, 100);
			}
		});
	});

	observer.observe(document.documentElement, {
		attributes: true,
		attributeFilter: ["data-theme"],
	});
</script>

<style>
	.giscus-container {
		/* 基础样式 */
		min-height: 200px;
		border-radius: 8px;
		padding: 1.5rem;
		margin-top: 4rem;
		background: var(--color-global-bg);
		border: 1px solid oklch(from var(--color-global-text) l c h / 0.1);
		transition: all 0.3s ease;
	}

	/* 标题样式 */
	.giscus-container h2 {
		color: var(--color-accent-2);
		font-size: 1.25rem;
		font-weight: 600;
		margin-bottom: 1.5rem;
		position: relative;
	}

	/* 分隔线样式 */
	.giscus-container hr {
		border: none;
		height: 1px;
		background: linear-gradient(
			90deg,
			transparent,
			oklch(from var(--color-accent) l c h / 0.3) 50%,
			transparent
		);
		margin-bottom: 2rem;
	}

	/* Giscus iframe 容器样式覆盖 */
	.giscus-container :global(.giscus) {
		/* 确保 iframe 继承容器样式 */
		border-radius: 6px;
		overflow: hidden;
	}

	/* 确保 iframe 完全透明，让容器背景显示 */
	.giscus-container :global(iframe.giscus-frame) {
		background: transparent !important;
		border: none !important;
		border-radius: 6px;
		width: 100% !important;
		min-height: 150px;
	}

	/* 响应式设计 */
	@media (max-width: 640px) {
		.giscus-container {
			margin-left: -1rem;
			margin-right: -1rem;
			padding: 1rem;
			border-radius: 0;
			border-left: none;
			border-right: none;
		}
	}

	/* 深色模式适配 */
	[data-theme="dark"] .giscus-container {
		background: oklch(from var(--color-global-bg) l c h / 0.8);
		border-color: oklch(from var(--color-global-text) l c h / 0.15);
		backdrop-filter: blur(10px);
	}

	/* 浅色模式适配 */
	[data-theme="light"] .giscus-container {
		background: oklch(from var(--color-global-bg) l c h / 0.9);
		border-color: oklch(from var(--color-global-text) l c h / 0.08);
		box-shadow: 0 1px 3px oklch(from var(--color-global-text) l c h / 0.05);
	}

	/* Hover 效果 */
	.giscus-container:hover {
		border-color: oklch(from var(--color-accent) l c h / 0.2);
		box-shadow: 0 4px 12px oklch(from var(--color-accent) l c h / 0.1);
	}

	[data-theme="dark"] .giscus-container:hover {
		border-color: oklch(from var(--color-accent) l c h / 0.3);
		box-shadow: 0 4px 12px oklch(from var(--color-accent) l c h / 0.15);
	}

	/* 加载状态样式 */
	.giscus-container.loading {
		position: relative;
		overflow: hidden;
	}

	.giscus-container.loading::before {
		content: "";
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background: linear-gradient(
			90deg,
			transparent,
			oklch(from var(--color-accent) l c h / 0.1),
			transparent
		);
		animation: shimmer 2s infinite;
	}

	@keyframes shimmer {
		0% {
			left: -100%;
		}
		100% {
			left: 100%;
		}
	}

	/* 确保评论内容与网站风格一致 */
	.giscus-container :global(.giscus-frame) {
		/* 这些样式会通过 postMessage 传递给 iframe */
		color-scheme: light dark;
	}

	/* 减少动画效果（尊重用户偏好） */
	@media (prefers-reduced-motion: reduce) {
		.giscus-container,
		.giscus-container::before {
			transition: none;
			animation: none;
		}
	}
</style>
